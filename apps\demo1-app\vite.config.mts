import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import { resolve } from 'path';

export default defineConfig({
  plugins: [vue()],
  
  // 微前端配置
  define: {
    __VUE_PROD_HYDRATION_MISMATCH_DETAILS__: 'false',
  },
  
  // 构建配置
  build: {
    target: 'es2015',
    cssCodeSplit: false,
    rollupOptions: {
      external: ['vue', 'vue-router', 'pinia', 'element-plus'],
      output: {
        globals: {
          vue: 'Vue',
          'vue-router': 'VueRouter',
          pinia: 'Pinia',
          'element-plus': 'ElementPlus',
        },
        // 输出格式支持UMD，便于主应用加载
        format: 'umd',
        name: 'Demo1App',
        entryFileNames: 'demo1-app.js',
        chunkFileNames: 'demo1-app-[name].js',
        assetFileNames: 'demo1-app-[name].[ext]',
      },
    },
  },
  
  // 开发服务器配置
  server: {
    port: 3001,
    cors: true,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  },
  
  // 路径解析
  resolve: {
    alias: {
      '#': resolve(__dirname, 'src'),
      '@': resolve(__dirname, 'src'),
    },
  },
  
  // CSS 配置
  css: {
    preprocessorOptions: {
      scss: {
        additionalData: `@use "@vben/styles/scss/index.scss" as *;`,
      },
    },
  },
});
