import { ref, computed } from 'vue';
import { defineStore } from 'pinia';
import dayjs from 'dayjs';
import { cloneDeep } from 'lodash-es';

export interface Demo1State {
  userInfo: {
    id: number;
    name: string;
    email: string;
    avatar: string;
  };
  statistics: {
    totalUsers: number;
    totalOrders: number;
    totalRevenue: number;
    lastUpdated: string;
  };
  settings: {
    theme: 'light' | 'dark';
    language: 'zh-CN' | 'en-US';
    autoRefresh: boolean;
    refreshInterval: number;
  };
}

export const useDemo1Store = defineStore('demo1', () => {
  // 状态
  const state = ref<Demo1State>({
    userInfo: {
      id: 1,
      name: 'Demo1 用户',
      email: '<EMAIL>',
      avatar: '/avatar.png',
    },
    statistics: {
      totalUsers: 1234,
      totalOrders: 5678,
      totalRevenue: 123456.78,
      lastUpdated: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    },
    settings: {
      theme: 'light',
      language: 'zh-CN',
      autoRefresh: true,
      refreshInterval: 30000,
    },
  });

  // 计算属性
  const formattedRevenue = computed(() => {
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: 'CNY',
    }).format(state.value.statistics.totalRevenue);
  });

  const isAutoRefreshEnabled = computed(() => {
    return state.value.settings.autoRefresh;
  });

  // 方法
  const updateUserInfo = (userInfo: Partial<Demo1State['userInfo']>) => {
    state.value.userInfo = { ...state.value.userInfo, ...userInfo };
  };

  const updateStatistics = (statistics: Partial<Demo1State['statistics']>) => {
    state.value.statistics = {
      ...state.value.statistics,
      ...statistics,
      lastUpdated: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    };
  };

  const updateSettings = (settings: Partial<Demo1State['settings']>) => {
    state.value.settings = { ...state.value.settings, ...settings };
  };

  const resetState = () => {
    state.value = cloneDeep({
      userInfo: {
        id: 1,
        name: 'Demo1 用户',
        email: '<EMAIL>',
        avatar: '/avatar.png',
      },
      statistics: {
        totalUsers: 0,
        totalOrders: 0,
        totalRevenue: 0,
        lastUpdated: dayjs().format('YYYY-MM-DD HH:mm:ss'),
      },
      settings: {
        theme: 'light',
        language: 'zh-CN',
        autoRefresh: true,
        refreshInterval: 30000,
      },
    });
  };

  const refreshStatistics = async () => {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    updateStatistics({
      totalUsers: Math.floor(Math.random() * 10000),
      totalOrders: Math.floor(Math.random() * 50000),
      totalRevenue: Math.random() * 1000000,
    });
  };

  return {
    // 状态
    state,
    
    // 计算属性
    formattedRevenue,
    isAutoRefreshEnabled,
    
    // 方法
    updateUserInfo,
    updateStatistics,
    updateSettings,
    resetState,
    refreshStatistics,
  };
});
