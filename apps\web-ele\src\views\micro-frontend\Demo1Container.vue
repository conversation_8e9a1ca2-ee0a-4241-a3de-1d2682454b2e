<script setup lang="ts">
import { computed } from 'vue';
import { useRoute } from 'vue-router';
import MicroAppContainer from '#/components/micro-frontend/MicroAppContainer.vue';

const route = useRoute();

// 计算微前端应用的基础路径
const basePath = computed(() => {
  return '/demo1';
});

// 微前端应用URL（开发环境和生产环境）
const appUrl = computed(() => {
  if (import.meta.env.DEV) {
    // 开发环境：直接访问demo1-app的开发服务器
    return 'http://localhost:3001/demo1-app.js';
  } else {
    // 生产环境：从CDN或静态资源服务器加载
    return '/static/micro-apps/demo1-app.js';
  }
});

// 传递给微前端的属性
const microAppProps = computed(() => ({
  // 当前路由信息
  currentPath: route.path,
  // 用户信息（如果需要）
  // userInfo: userStore.userInfo,
  // 其他共享状态
}));
</script>

<template>
  <div class="demo1-container">
    <MicroAppContainer
      app-name="Demo1应用"
      :app-url="appUrl"
      :base-path="basePath"
      :props="microAppProps"
    />
  </div>
</template>

<style scoped>
.demo1-container {
  width: 100%;
  height: 100%;
  min-height: calc(100vh - 200px);
}
</style>
