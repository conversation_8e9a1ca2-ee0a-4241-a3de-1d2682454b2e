{"demo1": {"title": "Demo1 Module", "overview": {"title": "Module Overview", "description": "This is the main page of Demo1 module", "moduleInfo": "Module Information", "currentTime": "Current Time", "moduleVersion": "Module Version", "developmentTeam": "Development Team", "featureList": "Feature List", "statistics": "Statistics", "userCount": "Users", "orderCount": "Orders", "revenue": "Revenue"}, "element": {"title": "Element Plus Demo", "primaryButton": "Primary Button", "successButton": "Success Button", "infoButton": "Info Button", "warningButton": "Warning Button", "dangerButton": "<PERSON>", "inputPlaceholder": "Please enter content", "tableHeaders": {"date": "Date", "name": "Name", "address": "Address"}}, "form": {"title": "Form Demo", "username": "Username", "email": "Email", "age": "Age", "gender": "Gender", "male": "Male", "female": "Female", "hobbies": "Hobbies", "reading": "Reading", "music": "Music", "sports": "Sports", "submit": "Submit", "reset": "Reset", "submitSuccess": "Form submitted successfully!", "resetSuccess": "Form has been reset"}, "features": {"elementDemo": "Element Plus Component Demo", "formHandling": "Form Handling Feature", "dataDisplay": "Data Display Feature", "routeManagement": "Route Management Feature"}}}