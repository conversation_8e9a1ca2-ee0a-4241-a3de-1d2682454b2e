import { createApp } from 'vue';
import { createRouter, createWebHistory } from 'vue-router';
import { createPinia } from 'pinia';
import ElementPlus from 'element-plus';
import 'element-plus/dist/index.css';

import App from './app.vue';
import { routes } from './router/routes';

// 微前端生命周期接口
interface MicroAppLifecycle {
  mount: (container: HTMLElement, props?: any) => Promise<void>;
  unmount: () => Promise<void>;
  update?: (props: any) => Promise<void>;
}

let app: any = null;
let router: any = null;
let pinia: any = null;

// 微前端挂载函数
async function mount(container: HTMLElement, props: any = {}) {
  // 创建路由实例
  router = createRouter({
    history: createWebHistory(props.basePath || '/demo1'),
    routes,
  });

  // 创建状态管理
  pinia = createPinia();

  // 创建应用实例
  app = createApp(App);
  
  app.use(router);
  app.use(pinia);
  app.use(ElementPlus);

  // 挂载到指定容器
  app.mount(container);

  console.log('Demo1 App mounted successfully');
}

// 微前端卸载函数
async function unmount() {
  if (app) {
    app.unmount();
    app = null;
    router = null;
    pinia = null;
    console.log('Demo1 App unmounted successfully');
  }
}

// 微前端更新函数
async function update(props: any) {
  console.log('Demo1 App updated with props:', props);
}

// 导出生命周期函数
const microApp: MicroAppLifecycle = {
  mount,
  unmount,
  update,
};

// 独立运行模式
if (!window.__MICRO_APP_ENVIRONMENT__) {
  const container = document.getElementById('app');
  if (container) {
    mount(container);
  }
}

// 微前端模式导出
if (window.__MICRO_APP_ENVIRONMENT__) {
  window.microApp = microApp;
}

export default microApp;
