<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick } from 'vue';

interface Props {
  appName: string;
  appUrl: string;
  basePath?: string;
  props?: Record<string, any>;
}

const props = withDefaults(defineProps<Props>(), {
  basePath: '',
  props: () => ({}),
});

const containerRef = ref<HTMLElement>();
const loading = ref(true);
const error = ref<string>('');

let microApp: any = null;

// 加载微前端应用
const loadMicroApp = async () => {
  try {
    loading.value = true;
    error.value = '';

    // 动态加载微前端脚本
    const script = document.createElement('script');
    script.src = props.appUrl;
    script.onload = async () => {
      try {
        // 获取微前端实例
        microApp = (window as any).microApp;
        
        if (microApp && microApp.mount && containerRef.value) {
          await microApp.mount(containerRef.value, {
            basePath: props.basePath,
            ...props.props,
          });
          loading.value = false;
        } else {
          throw new Error('微前端应用加载失败');
        }
      } catch (err) {
        error.value = `微前端挂载失败: ${err}`;
        loading.value = false;
      }
    };
    
    script.onerror = () => {
      error.value = '微前端脚本加载失败';
      loading.value = false;
    };

    document.head.appendChild(script);
  } catch (err) {
    error.value = `加载微前端失败: ${err}`;
    loading.value = false;
  }
};

// 卸载微前端应用
const unloadMicroApp = async () => {
  if (microApp && microApp.unmount) {
    try {
      await microApp.unmount();
      microApp = null;
    } catch (err) {
      console.error('微前端卸载失败:', err);
    }
  }
};

onMounted(async () => {
  await nextTick();
  await loadMicroApp();
});

onUnmounted(() => {
  unloadMicroApp();
});

// 重新加载
const reload = () => {
  unloadMicroApp().then(() => {
    loadMicroApp();
  });
};

defineExpose({
  reload,
});
</script>

<template>
  <div class="micro-app-container">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-loading-spinner />
      <p class="mt-2">正在加载 {{ appName }}...</p>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="error-container">
      <el-alert
        :title="`${appName} 加载失败`"
        :description="error"
        type="error"
        show-icon
      />
      <el-button type="primary" class="mt-4" @click="reload">
        重新加载
      </el-button>
    </div>

    <!-- 微前端容器 -->
    <div
      v-else
      ref="containerRef"
      class="micro-app-content"
    />
  </div>
</template>

<style scoped>
.micro-app-container {
  width: 100%;
  height: 100%;
  min-height: 400px;
}

.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  text-align: center;
}

.micro-app-content {
  width: 100%;
  height: 100%;
}
</style>
