/**
 * 微前端应用配置
 */

export interface MicroAppConfig {
  name: string;
  title: string;
  entry: string;
  devEntry?: string;
  basePath: string;
  activeRule: string;
  container?: string;
  props?: Record<string, any>;
}

export interface MicroAppMenuConfig {
  name: string;
  path: string;
  title: string;
  icon?: string;
  order?: number;
  children?: MicroAppMenuConfig[];
}

// 微前端应用配置
export const microAppsConfig: Record<string, MicroAppConfig> = {
  demo1: {
    name: 'demo1-app',
    title: 'Demo1应用',
    entry: '/static/micro-apps/demo1-app.js',
    devEntry: 'http://localhost:3001/demo1-app.js',
    basePath: '/demo1',
    activeRule: '/demo1',
    props: {
      // 可以传递给微前端的属性
    },
  },
  // 未来的demo2配置
  demo2: {
    name: 'demo2-app',
    title: 'Demo2应用',
    entry: '/static/micro-apps/demo2-app.js',
    devEntry: 'http://localhost:3002/demo2-app.js',
    basePath: '/demo2',
    activeRule: '/demo2',
    props: {},
  },
};

// 微前端菜单配置
export const microAppsMenuConfig: Record<string, MicroAppMenuConfig> = {
  demo1: {
    name: 'Demo1',
    path: '/demo1',
    title: 'Demo1模块',
    icon: 'ic:baseline-science',
    order: 2000,
    children: [
      {
        name: 'Demo1Overview',
        path: '/demo1/overview',
        title: '模块概览',
        icon: 'ic:baseline-dashboard',
      },
      {
        name: 'Demo1Element',
        path: '/demo1/element',
        title: 'Element Plus演示',
        icon: 'ic:baseline-widgets',
      },
      {
        name: 'Demo1Form',
        path: '/demo1/form',
        title: '表单演示',
        icon: 'ic:baseline-edit-note',
      },
    ],
  },
  demo2: {
    name: 'Demo2',
    path: '/demo2',
    title: 'Demo2模块',
    icon: 'ic:baseline-extension',
    order: 2100,
    children: [
      {
        name: 'Demo2Overview',
        path: '/demo2/overview',
        title: '模块概览',
        icon: 'ic:baseline-dashboard',
      },
    ],
  },
};

// 获取微前端应用配置
export function getMicroAppConfig(name: string): MicroAppConfig | undefined {
  return microAppsConfig[name];
}

// 获取微前端菜单配置
export function getMicroAppMenuConfig(name: string): MicroAppMenuConfig | undefined {
  return microAppsMenuConfig[name];
}

// 获取所有启用的微前端应用
export function getEnabledMicroApps(): string[] {
  // 可以根据环境变量或配置来控制哪些微前端应用启用
  const enabledApps = import.meta.env.VITE_ENABLED_MICRO_APPS?.split(',') || [];
  return enabledApps.length > 0 ? enabledApps : ['demo1']; // 默认启用demo1
}

// 生成微前端路由配置
export function generateMicroAppRoutes() {
  const enabledApps = getEnabledMicroApps();
  
  return enabledApps.map(appName => {
    const config = getMicroAppConfig(appName);
    const menuConfig = getMicroAppMenuConfig(appName);
    
    if (!config || !menuConfig) {
      return null;
    }

    return {
      component: 'BasicLayout',
      meta: {
        icon: menuConfig.icon,
        order: menuConfig.order,
        title: menuConfig.title,
      },
      name: menuConfig.name,
      path: menuConfig.path,
      children: [
        {
          name: `${menuConfig.name}MicroApp`,
          path: `${menuConfig.path}/:pathMatch(.*)*`,
          component: `/micro-frontend/${menuConfig.name}Container`,
          meta: {
            title: menuConfig.title,
            hideInMenu: true,
          },
        },
      ],
    };
  }).filter(Boolean);
}
