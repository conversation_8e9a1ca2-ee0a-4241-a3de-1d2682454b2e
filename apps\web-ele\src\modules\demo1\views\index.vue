<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { Check } from '@element-plus/icons-vue';
import dayjs from 'dayjs';
import { cloneDeep } from 'lodash-es';

// 当前时间
const currentTime = ref('');

// 模块信息
const moduleInfo = ref({
  version: '1.0.0',
  team: 'B小组',
});

// 功能列表
const features = ref([
  { id: 1, name: 'Element Plus 组件演示' },
  { id: 2, name: '表单处理功能' },
  { id: 3, name: '数据展示功能' },
  { id: 4, name: '路由管理功能' },
]);

// 统计数据
const stats = ref({
  users: 1234,
  orders: 5678,
  revenue: 123456.78,
});

// 格式化货币
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY',
  }).format(amount);
};

// 更新时间
const updateTime = () => {
  currentTime.value = dayjs().format('YYYY-MM-DD HH:mm:ss');
};

onMounted(() => {
  updateTime();
  // 每秒更新时间
  setInterval(updateTime, 1000);

  // 使用 lodash 深拷贝数据（演示外部依赖使用）
  const clonedStats = cloneDeep(stats.value);
  console.log('克隆的统计数据:', clonedStats);
});
</script>

<template>
  <div class="p-4">
    <h1 class="mb-4 text-2xl font-bold">Demo1 模块概览</h1>
    <div class="space-y-4">
      <el-card class="box-card">
        <template #header>
          <div class="card-header">
            <span>模块信息</span>
          </div>
        </template>
        <p class="mb-2">这是 Demo1 模块的主页面</p>
        <p class="mb-2">当前时间: {{ currentTime }}</p>
        <p class="mb-2">模块版本: {{ moduleInfo.version }}</p>
        <p class="mb-2">开发团队: {{ moduleInfo.team }}</p>
      </el-card>

      <el-card class="box-card">
        <template #header>
          <div class="card-header">
            <span>功能列表</span>
          </div>
        </template>
        <ul class="space-y-2">
          <li v-for="feature in features" :key="feature.id" class="flex items-center">
            <el-icon class="mr-2 text-green-500"><Check /></el-icon>
            {{ feature.name }}
          </li>
        </ul>
      </el-card>

      <el-card class="box-card">
        <template #header>
          <div class="card-header">
            <span>统计数据</span>
          </div>
        </template>
        <div class="grid grid-cols-3 gap-4">
          <div class="text-center">
            <div class="text-2xl font-bold text-blue-500">{{ stats.users }}</div>
            <div class="text-sm text-gray-500">用户数</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-green-500">{{ stats.orders }}</div>
            <div class="text-sm text-gray-500">订单数</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-purple-500">{{ formatCurrency(stats.revenue) }}</div>
            <div class="text-sm text-gray-500">收入</div>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<style scoped>
.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.box-card {
  margin-bottom: 20px;
}
</style>
