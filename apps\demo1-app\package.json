{"name": "@vben/demo1-app", "version": "1.0.0", "description": "Demo1 独立应用模块", "type": "module", "scripts": {"dev": "vite --mode development --port 3001", "build": "vite build --mode production", "preview": "vite preview --port 3001", "typecheck": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>"}, "imports": {"#/*": "./src/*"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@vben-core/shadcn-ui": "workspace:*", "@vben/access": "workspace:*", "@vben/common-ui": "workspace:*", "@vben/constants": "workspace:*", "@vben/hooks": "workspace:*", "@vben/icons": "workspace:*", "@vben/layouts": "workspace:*", "@vben/locales": "workspace:*", "@vben/plugins": "workspace:*", "@vben/preferences": "workspace:*", "@vben/request": "workspace:*", "@vben/stores": "workspace:*", "@vben/styles": "workspace:*", "@vben/types": "workspace:*", "@vben/utils": "workspace:*", "@vueuse/core": "catalog:", "dayjs": "catalog:", "element-plus": "catalog:", "lodash-es": "^4.17.21", "pinia": "catalog:", "vue": "catalog:", "vue-router": "catalog:"}, "devDependencies": {"@types/lodash-es": "^4.17.12", "unplugin-element-plus": "catalog:"}}